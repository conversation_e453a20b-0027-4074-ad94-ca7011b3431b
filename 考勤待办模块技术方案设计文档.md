# 考勤待办模块技术方案设计文档

## 📋 项目概述

### 1.1 项目背景
基于考勤系统现有架构，设计一个待办事项模块来提醒管理员处理各种考勤配置和异常情况。

### 1.2 技术栈
- **后端框架**: Spring Boot 2.2.8 + MyBatis Plus
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **定时任务**: XXL-JOB
- **权限系统**: UCenter集成
- **架构模式**: DDD（领域驱动设计）

### 1.3 待办类型需求
| 提醒类型 | 触发场景 | 待办文案 | 操作行为 | 完成条件 |
|---|---|---|---|---|
| 考勤周期配置提醒 | 有日历配置但无考勤周期的国家 | 【考勤周期】：【国家名称】 未配置 | 跳转考勤周期新增页面 | 该国家成功配置考勤周期规则 |
| 打卡规则配置提醒 | 有日历配置但无打卡规则的国家 | 【打卡规则】：【国家名称】 未配置 | 跳转打卡规则新增页面 | 该国家成功配置打卡规则 |
| 固定班次规则提醒 | 有日历配置但无固定班次的国家 | 【固定班次规则】：【国家名称】 未配置 | 跳转固定班次新增页面 | 该国家成功配置固定班次规则 |
| 多班次规则提醒 | 多班次员工无规则匹配 | 【多班次】：【国家名称】有X人无多班次规则 | 跳转多班次配置页面 | 所有多班次员工都已配置规则 |
| 新员工入职提醒 | 新员工确认入职后需人工复核 | 【入职】：X人新入职，待确认规则 | 显示新入职员工列表 | 所有新员工考勤档案被查看 |
| 今日未排班提醒 | 权限范围内今日未排班员工 | 【国家名称】今日未排班X人 | 跳转排班计划页面 | 今日未排班人数为0 |
| 考勤异常待处理提醒 | 未处理的考勤异常记录 | 【国家名称】异常待处理 X 条 | 跳转员工考勤统计页面 | 异常数为0 |
| WPM仓内异常待处理提醒 | 未处理的WPM仓内异常记录 | 【国家名称】WPM仓内异常待处理 X 条 | 跳转入离仓计时页面 | 异常记录数为0 |

## 🗄️ 数据库设计

### 2.1 核心表结构

#### 2.1.1 待办事项主表 (attendance_todo_item)
```sql
CREATE TABLE `attendance_todo_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `todo_code` varchar(64) NOT NULL COMMENT '待办编码，唯一标识',
  `todo_type` varchar(32) NOT NULL COMMENT '待办类型',
  `title` varchar(255) NOT NULL COMMENT '待办标题',
  `description` text COMMENT '待办描述',
  `country_code` varchar(8) NOT NULL COMMENT '国家代码',
  `priority` tinyint(4) DEFAULT 1 COMMENT '优先级：1-低，2-中，3-高',
  `status` varchar(16) DEFAULT 'PENDING' COMMENT '状态：PENDING-待处理，COMPLETED-已完成，CANCELLED-已取消',
  `trigger_condition` json COMMENT '触发条件JSON',
  `completion_condition` json COMMENT '完成条件JSON',
  `action_url` varchar(512) COMMENT '操作跳转URL',
  `action_params` json COMMENT '操作参数JSON',
  `related_data_id` varchar(64) COMMENT '关联数据ID',
  `related_data_type` varchar(32) COMMENT '关联数据类型',
  `expire_date` datetime COMMENT '过期时间',
  `completed_date` datetime COMMENT '完成时间',
  `create_date` datetime NOT NULL COMMENT '创建时间',
  `create_user_code` varchar(32) COMMENT '创建人编码',
  `create_user_name` varchar(64) COMMENT '创建人姓名',
  `last_upd_date` datetime NOT NULL COMMENT '最后更新时间',
  `last_upd_user_code` varchar(32) COMMENT '最后更新人编码',
  `last_upd_user_name` varchar(64) COMMENT '最后更新人姓名',
  `is_delete` tinyint(4) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `record_version` bigint(20) DEFAULT 1 COMMENT '记录版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_todo_code` (`todo_code`),
  KEY `idx_todo_type_country` (`todo_type`, `country_code`),
  KEY `idx_status_create_date` (`status`, `create_date`),
  KEY `idx_country_status` (`country_code`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='待办事项主表';
```

#### 2.1.2 待办类型配置表 (attendance_todo_type_config)
```sql
CREATE TABLE `attendance_todo_type_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `todo_type` varchar(32) NOT NULL COMMENT '待办类型',
  `type_name_cn` varchar(64) NOT NULL COMMENT '类型中文名称',
  `type_name_en` varchar(64) NOT NULL COMMENT '类型英文名称',
  `detection_method` varchar(16) NOT NULL COMMENT '检测方式：SCHEDULE-定时检测，EVENT-事件触发，BOTH-两者都有',
  `detection_cron` varchar(64) COMMENT '定时检测表达式',
  `template_title_cn` varchar(255) COMMENT '标题模板（中文）',
  `template_title_en` varchar(255) COMMENT '标题模板（英文）',
  `default_action_url` varchar(512) COMMENT '默认操作URL',
  `is_enabled` tinyint(4) DEFAULT 1 COMMENT '是否启用：0-否，1-是',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  -- 基础字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_todo_type` (`todo_type`),
  KEY `idx_enabled_sort` (`is_enabled`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='待办类型配置表';
```

#### 2.1.3 用户待办关联表 (attendance_todo_user_relation)
```sql
CREATE TABLE `attendance_todo_user_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `todo_id` bigint(20) NOT NULL COMMENT '待办事项ID',
  `user_code` varchar(32) NOT NULL COMMENT '用户编码',
  `user_name` varchar(64) COMMENT '用户姓名',
  `assign_reason` varchar(255) COMMENT '分配原因',
  `read_status` tinyint(4) DEFAULT 0 COMMENT '阅读状态：0-未读，1-已读',
  `read_date` datetime COMMENT '阅读时间',
  `operation_status` varchar(16) DEFAULT 'PENDING' COMMENT '操作状态：PENDING-待操作，OPERATED-已操作',
  `operation_date` datetime COMMENT '操作时间',
  -- 基础字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_todo_user` (`todo_id`, `user_code`),
  KEY `idx_user_code_status` (`user_code`, `operation_status`),
  KEY `idx_todo_id` (`todo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户待办关联表';
```

#### 2.1.4 待办操作日志表 (attendance_todo_operation_log)
```sql
CREATE TABLE `attendance_todo_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `todo_id` bigint(20) NOT NULL COMMENT '待办事项ID',
  `user_code` varchar(32) NOT NULL COMMENT '操作用户编码',
  `user_name` varchar(64) COMMENT '操作用户姓名',
  `operation_type` varchar(32) NOT NULL COMMENT '操作类型：CREATE-创建，READ-阅读，OPERATE-操作，COMPLETE-完成，CANCEL-取消',
  `operation_desc` varchar(255) COMMENT '操作描述',
  `before_status` varchar(16) COMMENT '操作前状态',
  `after_status` varchar(16) COMMENT '操作后状态',
  `operation_data` json COMMENT '操作数据JSON',
  `create_date` datetime NOT NULL COMMENT '创建时间',
  `create_user_code` varchar(32) COMMENT '创建人编码',
  `create_user_name` varchar(64) COMMENT '创建人姓名',
  PRIMARY KEY (`id`),
  KEY `idx_todo_id_date` (`todo_id`, `create_date`),
  KEY `idx_user_code_date` (`user_code`, `create_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='待办操作日志表';
```

#### 2.1.5 待办统计表 (attendance_todo_statistics)
```sql
CREATE TABLE `attendance_todo_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `country_code` varchar(8) NOT NULL COMMENT '国家代码',
  `todo_type` varchar(32) NOT NULL COMMENT '待办类型',
  `total_count` int(11) DEFAULT 0 COMMENT '总数量',
  `pending_count` int(11) DEFAULT 0 COMMENT '待处理数量',
  `completed_count` int(11) DEFAULT 0 COMMENT '已完成数量',
  `cancelled_count` int(11) DEFAULT 0 COMMENT '已取消数量',
  `avg_completion_hours` decimal(10,2) COMMENT '平均完成时长（小时）',
  `create_date` datetime NOT NULL COMMENT '创建时间',
  `last_upd_date` datetime NOT NULL COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_date_country_type` (`stat_date`, `country_code`, `todo_type`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_country_date` (`country_code`, `stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='待办统计表';
```

### 2.2 索引优化策略
```sql
-- 复合索引优化
ALTER TABLE attendance_todo_item ADD INDEX idx_composite_query (country_code, status, todo_type, create_date);
ALTER TABLE attendance_todo_item ADD INDEX idx_expire_status (expire_date, status);
ALTER TABLE attendance_todo_user_relation ADD INDEX idx_user_status_read (user_code, operation_status, read_status);
```

## 🔄 核心业务流程设计

### 3.1 待办事项生成流程
1. **定时任务触发** - XXL-JOB按配置的cron表达式执行
2. **检测逻辑执行** - 根据待办类型执行相应的检测逻辑
3. **权限用户查询** - 获取有权限处理该国家待办的用户列表
4. **待办事项生成** - 创建待办记录并分发给相关用户
5. **通知发送** - 发送邮件/短信/APP推送通知

### 3.2 待办完成检测流程
1. **定时检测任务** - 每15分钟检测一次待办完成状态
2. **完成条件验证** - 根据待办类型验证完成条件
3. **状态更新** - 满足条件的待办标记为已完成
4. **日志记录** - 记录完成操作日志
5. **缓存清理** - 清理相关用户的缓存数据

### 3.3 核心服务设计

#### TodoDetectionService（待办检测服务）
```java
@Service
public class TodoDetectionService {
    /**
     * 检测考勤周期配置待办
     */
    public List<TodoItemDO> detectAttendanceCycleTodos();
    
    /**
     * 检测打卡规则配置待办
     */
    public List<TodoItemDO> detectPunchRuleTodos();
    
    /**
     * 检测固定班次规则待办
     */
    public List<TodoItemDO> detectFixedClassTodos();
    
    /**
     * 检测多班次规则待办
     */
    public List<TodoItemDO> detectMultiClassTodos();
    
    /**
     * 检测新员工入职待办
     */
    public List<TodoItemDO> detectNewEmployeeTodos();
    
    /**
     * 检测今日未排班待办
     */
    public List<TodoItemDO> detectUnscheduledTodos();
    
    /**
     * 检测考勤异常待办
     */
    public List<TodoItemDO> detectAbnormalTodos();
    
    /**
     * 检测WPM仓内异常待办
     */
    public List<TodoItemDO> detectWpmAbnormalTodos();
}
```

#### TodoPermissionService（待办权限服务）
```java
@Service
public class TodoPermissionService {
    /**
     * 获取有权限处理指定国家待办的用户列表
     */
    public List<String> getAuthorizedUsers(String countryCode, String todoType);
    
    /**
     * 检查用户是否有权限查看待办
     */
    public boolean hasPermission(String userCode, TodoItemDO todoItem);
    
    /**
     * 分发待办给相关用户
     */
    public void distributeTodoToUsers(TodoItemDO todoItem, List<String> userCodes);
}
```

## 🌐 接口层设计

### 4.1 TodoController（待办控制器）
```java
@RestController
@RequestMapping("/api/attendance/todo")
@Api(tags = "待办事项管理")
public class TodoController {
    
    /**
     * 获取用户待办列表
     */
    @PostMapping("/list")
    public Result<PageResult<TodoItemVO>> getTodoList(@RequestBody @Valid TodoListQuery query);
    
    /**
     * 获取待办详情
     */
    @GetMapping("/detail/{todoId}")
    public Result<TodoDetailVO> getTodoDetail(@PathVariable Long todoId);
    
    /**
     * 标记待办为已读
     */
    @PostMapping("/read/{todoId}")
    public Result<Void> markAsRead(@PathVariable Long todoId);
    
    /**
     * 批量标记待办为已读
     */
    @PostMapping("/batch-read")
    public Result<Void> batchMarkAsRead(@RequestBody @Valid BatchOperationRequest request);
    
    /**
     * 执行待办操作（跳转）
     */
    @PostMapping("/operate/{todoId}")
    public Result<TodoOperationVO> operateTodo(@PathVariable Long todoId);
    
    /**
     * 获取待办统计信息
     */
    @PostMapping("/statistics")
    public Result<TodoStatisticsVO> getTodoStatistics(@RequestBody @Valid TodoStatisticsQuery query);
    
    /**
     * 获取待办类型配置
     */
    @GetMapping("/types")
    public Result<List<TodoTypeConfigVO>> getTodoTypes();
}
```

### 4.2 核心DTO设计

#### TodoListQuery（待办列表查询）
```java
@Data
@EqualsAndHashCode(callSuper = true)
public class TodoListQuery extends PermissionBaseQuery {
    private List<String> todoTypes;        // 待办类型
    private List<String> statuses;         // 待办状态
    private List<String> countryCodes;     // 国家代码
    private Integer priority;              // 优先级
    private Integer readStatus;            // 阅读状态
    private Date createDateStart;          // 创建时间开始
    private Date createDateEnd;            // 创建时间结束
    private String sortField = "create_date";     // 排序字段
    private String sortDirection = "DESC";        // 排序方向
}
```

#### TodoItemVO（待办事项视图对象）
```java
@Data
public class TodoItemVO {
    private Long id;                       // 待办ID
    private String todoCode;               // 待办编码
    private String todoType;               // 待办类型
    private String todoTypeName;           // 待办类型名称
    private String title;                  // 标题
    private String description;            // 描述
    private String countryCode;            // 国家代码
    private String countryName;            // 国家名称
    private Integer priority;              // 优先级
    private String priorityName;           // 优先级名称
    private String status;                 // 状态
    private String statusName;             // 状态名称
    private Integer readStatus;            // 阅读状态
    private String actionUrl;              // 操作URL
    private Map<String, Object> actionParams;  // 操作参数
    private Date expireDate;               // 过期时间
    private Date createDate;               // 创建时间
    private Boolean isExpired;             // 是否过期
    private Long hoursToExpire;            // 距离过期时间（小时）
}
```

## 🔐 权限集成方案

### 5.1 权限映射关系
```java
@Getter
public enum TodoPermissionMapping {
    ATTENDANCE_CYCLE("ATTENDANCE_CYCLE", "考勤周期配置", 
                    Arrays.asList(SystemRoleEnum.ATTENDANCE_SYS, SystemRoleEnum.AREA_SYS),
                    PermissionTypeEnum.COUNTRY),
    
    PUNCH_RULE("PUNCH_RULE", "打卡规则配置",
              Arrays.asList(SystemRoleEnum.ATTENDANCE_SYS, SystemRoleEnum.AREA_SYS),
              PermissionTypeEnum.COUNTRY),
    
    // ... 其他待办类型映射
}
```

### 5.2 权限服务实现
- **用户权限缓存**: Redis缓存用户权限信息，过期时间30分钟
- **批量权限查询**: 支持批量查询用户权限，提高性能
- **权限变更通知**: 权限变更时自动清理相关缓存

## ⚡ 性能优化策略

### 6.1 缓存策略
- **多层缓存架构**: Redis + 本地缓存
- **缓存分级**: 不同数据类型设置不同的过期时间
- **缓存预热**: 系统启动时预加载热点数据
- **缓存失效**: 数据变更时精确失效相关缓存

### 6.2 数据库优化
- **索引优化**: 针对查询场景设计复合索引
- **分页查询**: 大数据量查询使用游标分页
- **批量操作**: 批量插入/更新提高性能
- **读写分离**: 查询使用只读库，写操作使用主库

### 6.3 并发处理
- **异步处理**: 待办生成和通知发送异步执行
- **线程池**: 合理配置线程池大小
- **限流控制**: 防止系统过载
- **熔断机制**: 依赖服务异常时的降级处理

## 🔗 系统集成方案

### 7.1 与现有系统集成点
- **考勤配置系统**: 复用现有的配置查询逻辑
- **异常处理系统**: 集成现有的异常统计接口
- **权限系统**: 基于UCenter权限系统进行用户授权
- **通知系统**: 复用现有的邮件/短信通知服务

### 7.2 定时任务集成
```java
@Component
public class TodoDetectionJobHandler {
    
    /**
     * 待办事项检测任务 - 每30分钟执行一次
     */
    @XxlJob("todoDetectionHandler")
    public ReturnT<String> todoDetectionHandler(String param);
    
    /**
     * 待办完成检测任务 - 每15分钟执行一次
     */
    @XxlJob("todoCompletionCheckHandler")
    public ReturnT<String> todoCompletionCheckHandler(String param);
    
    /**
     * 待办统计数据更新任务 - 每天凌晨1点执行
     */
    @XxlJob("todoStatisticsUpdateHandler")
    public ReturnT<String> todoStatisticsUpdateHandler(String param);
}
```

## 📊 监控与运维

### 8.1 监控指标
- **待办生成量**: 每日/每小时待办生成数量
- **待办完成率**: 待办完成率统计
- **系统性能**: 接口响应时间、数据库连接池状态
- **异常监控**: 系统异常、业务异常统计

### 8.2 日志记录
- **业务日志**: 待办生成、完成、操作等关键业务日志
- **性能日志**: 慢查询、接口耗时等性能日志
- **错误日志**: 系统异常、业务异常详细日志

## 🚀 部署方案

### 9.1 数据库迁移
- **版本控制**: 使用Flyway进行数据库版本管理
- **增量更新**: 支持增量数据库结构更新
- **回滚机制**: 支持数据库结构回滚

### 9.2 应用部署
- **灰度发布**: 支持灰度发布，降低发布风险
- **配置管理**: 使用Apollo配置中心管理配置
- **健康检查**: 提供健康检查接口

### 9.3 配置示例
```yaml
attendance:
  todo:
    detection:
      enabled: true
      batch-size: 1000
      max-retry-times: 3
    cache:
      enabled: true
      expire-minutes: 15
      max-size: 10000
    notification:
      enabled: true
      channels: ["email", "sms", "app"]
```

## 📈 项目计划

### 10.1 开发周期
- **总体周期**: 4-5周
- **数据库设计**: 3天
- **核心业务开发**: 2周
- **接口开发**: 1周
- **集成测试**: 1周
- **部署上线**: 3天

### 10.2 风险评估
- **技术风险**: 低（基于成熟技术栈）
- **集成风险**: 中（需要与多个现有系统集成）
- **性能风险**: 低（有完整的性能优化方案）
- **运维风险**: 低（有完整的监控和日志方案）

---

## 📋 附录

### A. 待办类型配置初始化数据
```sql
-- 初始化待办类型配置数据
INSERT INTO `attendance_todo_type_config`
(`todo_type`, `type_name_cn`, `type_name_en`, `detection_method`, `detection_cron`,
 `template_title_cn`, `template_title_en`, `default_action_url`, `is_enabled`, `sort_order`,
 `create_date`, `create_user_code`, `create_user_name`, `last_upd_date`,
 `last_upd_user_code`, `last_upd_user_name`, `is_delete`, `record_version`)
VALUES
('ATTENDANCE_CYCLE', '考勤周期配置', 'Attendance Cycle Config', 'SCHEDULE', '0 */30 * * * ?',
 '【考勤周期】：{countryName} 未配置', '【Attendance Cycle】: {countryName} Not Configured',
 '/attendance/cycle/add', 1, 1, NOW(), 'SYSTEM', 'SYSTEM', NOW(), 'SYSTEM', 'SYSTEM', 0, 1),

('PUNCH_RULE', '打卡规则配置', 'Punch Rule Config', 'SCHEDULE', '0 */30 * * * ?',
 '【打卡规则】：{countryName} 未配置', '【Punch Rule】: {countryName} Not Configured',
 '/attendance/punch-rule/add', 1, 2, NOW(), 'SYSTEM', 'SYSTEM', NOW(), 'SYSTEM', 'SYSTEM', 0, 1),

('FIXED_CLASS', '固定班次规则', 'Fixed Class Rule', 'SCHEDULE', '0 */30 * * * ?',
 '【固定班次规则】：{countryName} 未配置', '【Fixed Class Rule】: {countryName} Not Configured',
 '/attendance/fixed-class/add', 1, 3, NOW(), 'SYSTEM', 'SYSTEM', NOW(), 'SYSTEM', 'SYSTEM', 0, 1),

('MULTI_CLASS', '多班次规则', 'Multi Class Rule', 'SCHEDULE', '0 */15 * * * ?',
 '【多班次】：{countryName}有{count}人无多班次规则', '【Multi Class】: {countryName} has {count} people without multi-class rules',
 '/attendance/multi-class/config', 1, 4, NOW(), 'SYSTEM', 'SYSTEM', NOW(), 'SYSTEM', 'SYSTEM', 0, 1),

('NEW_EMPLOYEE', '新员工入职', 'New Employee Onboarding', 'EVENT', NULL,
 '【入职】：{count}人新入职，待确认规则', '【Onboarding】: {count} new employees, pending rule confirmation',
 '/attendance/employee/new-list', 1, 5, NOW(), 'SYSTEM', 'SYSTEM', NOW(), 'SYSTEM', 'SYSTEM', 0, 1),

('UNSCHEDULED', '今日未排班', 'Unscheduled Today', 'SCHEDULE', '0 0 8 * * ?',
 '{countryName}今日未排班{count}人', '{countryName} has {count} unscheduled people today',
 '/attendance/scheduling/unscheduled', 1, 6, NOW(), 'SYSTEM', 'SYSTEM', NOW(), 'SYSTEM', 'SYSTEM', 0, 1),

('ABNORMAL', '考勤异常待处理', 'Attendance Abnormal Pending', 'SCHEDULE', '0 */10 * * * ?',
 '{countryName}异常待处理 {count} 条', '{countryName} has {count} abnormal records pending',
 '/attendance/abnormal/list', 1, 7, NOW(), 'SYSTEM', 'SYSTEM', NOW(), 'SYSTEM', 'SYSTEM', 0, 1),

('WPM_ABNORMAL', 'WPM仓内异常待处理', 'WPM Warehouse Abnormal Pending', 'SCHEDULE', '0 */10 * * * ?',
 '{countryName}WPM仓内异常待处理 {count} 条', '{countryName} has {count} WPM warehouse abnormal records pending',
 '/warehouse/abnormal/list', 1, 8, NOW(), 'SYSTEM', 'SYSTEM', NOW(), 'SYSTEM', 'SYSTEM', 0, 1);
```

### B. XXL-JOB任务配置
```json
{
  "jobGroup": "attendance-todo",
  "jobDesc": "待办事项检测任务",
  "jobCron": "0 */30 * * * ?",
  "executorHandler": "todoDetectionHandler",
  "executorParam": "{\"todoTypes\":[\"ATTENDANCE_CYCLE\",\"PUNCH_RULE\",\"FIXED_CLASS\"]}",
  "executorRouteStrategy": "FIRST",
  "executorBlockStrategy": "SERIAL_EXECUTION",
  "executorTimeout": 300,
  "executorFailRetryCount": 3,
  "glueType": "BEAN",
  "triggerStatus": 1
}
```

### C. 前端集成示例
```javascript
// 待办列表组件示例
const TodoList = {
  data() {
    return {
      todoList: [],
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        total: 0
      }
    }
  },
  methods: {
    // 获取待办列表
    async fetchTodoList() {
      this.loading = true;
      try {
        const response = await this.$http.post('/api/attendance/todo/list', {
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          todoTypes: this.selectedTypes,
          statuses: this.selectedStatuses
        });

        this.todoList = response.data.records;
        this.pagination.total = response.data.total;
      } catch (error) {
        this.$message.error('获取待办列表失败');
      } finally {
        this.loading = false;
      }
    },

    // 标记为已读
    async markAsRead(todoId) {
      try {
        await this.$http.post(`/api/attendance/todo/read/${todoId}`);
        this.$message.success('标记成功');
        this.fetchTodoList();
      } catch (error) {
        this.$message.error('操作失败');
      }
    },

    // 执行待办操作
    async operateTodo(todo) {
      try {
        const response = await this.$http.post(`/api/attendance/todo/operate/${todo.id}`);
        const { actionUrl, actionParams } = response.data;

        // 跳转到操作页面
        this.$router.push({
          path: actionUrl,
          query: actionParams
        });
      } catch (error) {
        this.$message.error('操作失败');
      }
    }
  }
}
```

### D. 性能测试指标
- **并发用户数**: 支持1000+并发用户
- **响应时间**:
  - 待办列表查询: < 200ms
  - 待办详情查询: < 100ms
  - 待办操作: < 300ms
- **吞吐量**:
  - 待办生成: 10000+/小时
  - 待办查询: 50000+/小时
- **数据库性能**:
  - 查询QPS: 5000+
  - 写入TPS: 1000+

---

## 📝 总结

本技术方案基于考勤系统现有架构，设计了完整的待办模块，具有以下特点：

1. **架构完整性**: 基于现有DDD架构，保持系统一致性
2. **性能优化**: 多层缓存 + 批量处理 + 并行查询
3. **权限安全**: 完整的权限映射和缓存机制
4. **扩展性强**: 支持8种待办类型，易于扩展新类型
5. **运维友好**: 完整的监控、日志和统计功能

整个方案技术风险可控，预计开发周期4-5周，能够满足业务需求并具备良好的扩展性。

---

**文档版本**: v1.0
**编写人**: Claude 4.0 sonnet
**编写日期**: 2025-08-14
**审核状态**: 待审核
